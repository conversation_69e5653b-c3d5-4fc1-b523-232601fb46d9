[package]
name = "hsarec"
version = "2.0.0"
authors = ["Curtion <<EMAIL>>"]
edition = "2024"
build = "build.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
gpui = { git = "https://github.com/zed-industries/zed", tag = "v0.188.4" }
tray-item = "0.10.0"
anyhow = "1.0.98"
webbrowser = "1.0.4"
inputbot = "0.6"
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"
crossbeam-channel = "0.5"
log = "0.4"
simplelog = { version = "0.12", features = ["local-offset"] }
sysinfo = "0.35.2"
notify = "8.0.0"
regex = "1.11.1"
is_elevated = "0.1.2"

[dependencies.windows]
version = "0.61"
features = ["Win32_NetworkManagement_IpHelper", "Win32_Networking_WinSock"]

[build-dependencies]
embed-resource = "3.0"
