# 内存占用优化说明

## 问题分析

您遇到的内存占用问题主要由以下几个原因造成：

### 1. 重复创建设置窗口
- 每次点击"设置快捷键"都会创建新的GPUI应用程序实例
- 没有检查是否已有窗口打开，导致多个窗口同时存在
- 每个窗口都会占用大量内存资源

### 2. 资源没有正确释放
- GPUI应用程序的资源在窗口关闭后没有完全清理
- 键盘事件监听器使用了`.detach()`，导致即使窗口关闭后仍然保持活跃
- 线程没有被正确管理和清理

### 3. 内存泄漏风险
- Channel和观察者可能在窗口关闭后仍然保持引用
- 没有适当的生命周期管理

## 解决方案

### 1. 添加窗口状态管理
```rust
// 在main.rs中添加
let setting_window_open = Arc<Mutex<bool>>::new(false);

// 在处理Setting消息时检查状态
let mut window_open = setting_window_open.lock().unwrap();
if *window_open {
    info!("设置窗口已经打开，忽略重复请求");
    continue;
}
*window_open = true;
```

### 2. 改进资源清理
```rust
// 在gui.rs中添加窗口关闭事件处理
window.update(cx, |_, _, cx| {
    let _ = cx.on_window_closed(|_| {
        log::info!("设置窗口已关闭");
    });
}).unwrap();
```

### 3. 优化键盘事件监听
```rust
// 移除.detach()调用，让订阅随应用程序生命周期自动管理
let _keystroke_subscription = cx.observe_keystrokes(move |event, _window, cx| {
    // 事件处理逻辑
});
```

### 4. 线程生命周期管理
```rust
// 在线程结束时重置窗口状态
std::thread::spawn(move || {
    let result = gui::app(gui_tx_clone, &reconnect_hotkey);
    // 窗口关闭后，重置状态
    if let Ok(mut window_open) = setting_window_open_clone.lock() {
        *window_open = false;
    }
    if let Err(e) = result {
        error!("GUI应用程序出错: {}", e);
    }
});
```

## 预期效果

实施这些优化后，您应该会看到：

1. **防止重复窗口创建** - 同时只能打开一个设置窗口
2. **更好的内存管理** - 窗口关闭后资源会被正确释放
3. **减少内存泄漏** - 事件监听器和订阅会随应用程序生命周期正确管理
4. **更稳定的性能** - 避免因多个窗口实例导致的内存累积

## 测试建议

1. 多次打开和关闭设置窗口
2. 使用任务管理器监控内存使用情况
3. 确认窗口关闭后内存占用确实下降
4. 验证不能同时打开多个设置窗口

## 进一步优化建议

如果问题仍然存在，可以考虑：

1. **使用单例模式** - 确保整个应用程序生命周期内只有一个设置窗口实例
2. **显式资源清理** - 在窗口关闭时手动清理所有相关资源
3. **内存分析工具** - 使用Rust的内存分析工具（如valgrind）来识别具体的内存泄漏点
4. **GPUI版本升级** - 检查是否有更新的GPUI版本修复了相关的内存管理问题
