# 构建步骤说明

1. Git 克隆本项目
2. 构建：`cargo build --release`
3. `target/release/hsarec.exe` 即为可执行文件文件

# 使用说明

1. [下载压缩包](https://github.com/Curtion/HearthStone-AutoReConn/releases)、解压
2. 双击运行 hsarec.exe
3. 右下角托盘菜单中可选择拔线操作，或者使用快捷键`Shift+Alt+R`快速拔线，目前重连速度为 3s

如果在断线过程中程序意外退出，可能会导致炉石无法连接网络，此时只需要重新运行 hsarec.exe 一次即可解决。

# 已知问题
多次拔线(第一次拔线)可能会出现游戏闪退的现象，不知是否是游戏机制，可以尝试把重连速度加快

# 申明

本程序不修改炉石传说游戏任何数据, 当前拔线使用`iphlpapi.dll`实现
